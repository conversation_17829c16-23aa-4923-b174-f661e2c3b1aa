<div align="center">
<h1 style="font-size: 5em;">LAND-REGISTRY-SYSTEM</h1>

# Transforming Land Ownership with Seamless Innovation
![javascript](https://img.shields.io/badge/javascript-99.5%25-blue?logo=javascript)
![languages](https://img.shields.io/badge/languages-4-informational?logo=code)
<br>

**Built with the tools and technologies:**
<br>
![React](https://img.shields.io/badge/React-61DAFB?logo=react&logoColor=black)
![npm](https://img.shields.io/badge/npm-CB3837?logo=npm&logoColor=white)
![Express](https://img.shields.io/badge/Express-black?logo=express&logoColor=white)
![JSON](https://img.shields.io/badge/JSON-000000?logo=json&logoColor=white)
![Markdown](https://img.shields.io/badge/Markdown-000000?logo=markdown&logoColor=white)
![Mongoose](https://img.shields.io/badge/Mongoose-880000?logo=mongoose&logoColor=white)
![PostCSS](https://img.shields.io/badge/PostCSS-DD3A0A?logo=postcss&logoColor=white)
![.ENV](https://img.shields.io/badge/.ENV-FCE566?logo=dotenv&logoColor=black)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?logo=javascript&logoColor=black)
![Nodemon](https://img.shields.io/badge/Nodemon-76D04B?logo=nodemon&logoColor=white)
![Cloudinary](https://img.shields.io/badge/Cloudinary-3448C5?logo=cloudinary&logoColor=white)
![Vite](https://img.shields.io/badge/Vite-646CFF?logo=vite&logoColor=white)
</div>

<br><br>


✨ **Transforming Land Ownership with Seamless Innovation**
- 🏛️ **Government-Grade Security** - Role-based access control and authentication
- 📄 **Document Management** - GridFS storage with verification workflows
- 💳 **Payment Integration** - Secure Chapa payment gateway integration
- ⚖️ **Legal Compliance** - Ethiopian property law standards
- 📊 **Comprehensive Reporting** - Real-time analytics and audit trails

## 📘 Overview
**land-registry-system** is an all-in-one developer toolkit for building secure, scalable land registration and property management platforms. It automates environment setup, manages complex workflows, and integrates with external payment gateways to streamline property transactions.

- 🛠️ **Automated Startup Orchestration** – Launches multiple services for development
- 🔒 **Role-Based Security** – Middleware, authentication, and authorization
- 📂 **Document & Payment Management** – GridFS, Chapa integration
- 📊 **Logging & Reporting** – Audit logs and real-time reporting
- ⚙️ **Configurable Architecture** – Env vars, port configs, and API endpoints

## 🏗️ System Architecture
### 🏛️ Land Officer Frontend
- **Purpose:** Admin interface for officials
- **Users:** Land officers, administrators
- **Features:** Verification, approval, reporting
- **Access:** `http://localhost:3000`

### 👥 User Frontend
- **Purpose:** Public interface
- **Users:** Property owners, citizens
- **Features:** Registration, document upload, Chapa payment
- **Access:** `http://localhost:3002`

### 🔗 Unified Backend
- **Purpose:** Centralized API and database management
- **Features:** Authentication, property management, payments, documents
- **Access:** `https://land-registry-backend-plum.vercel.app`

## ⚙️ Prerequisites
- Node.js v16+
- Git
- Internet connection (for unified backend)

## 🚀 1-Minute Setup
 Clone the repository
```bash
git clone https://github.com/your-repo/land-registry-system.git
cd land-registry-system
```

# 🌐 Access the Applications
- **Land Officer Portal**: `http://localhost:3000`
- **User Portal**: `http://localhost:3002`
- **Unified Backend**: `https://land-registry-backend-plum.vercel.app`
