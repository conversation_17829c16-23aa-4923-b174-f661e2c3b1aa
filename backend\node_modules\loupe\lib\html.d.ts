import type { Options } from './types.js';
export declare function inspectAttribute([key, value]: [unknown, unknown], options: Options): string;
export declare function inspectNodeCollection(collection: ArrayLike<Node>, options: Options): string;
export declare function inspectNode(node: Node, options: Options): string;
export default function inspectHTML(element: Element, options: Options): string;
//# sourceMappingURL=html.d.ts.map