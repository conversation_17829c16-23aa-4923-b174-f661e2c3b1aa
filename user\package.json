{"name": "land-registry-user-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.0.18", "axios": "^1.6.2", "chart.js": "^4.4.9", "formik": "^2.4.5", "framer-motion": "^10.16.4", "leaflet": "^1.9.4", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.0", "react-toastify": "^9.1.3", "yup": "^1.3.2"}, "devDependencies": {"@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.23.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^5.0.0"}}