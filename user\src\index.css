@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f9fafb;
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark;
  }

  .btn-danger {
    @apply bg-accent text-white hover:bg-accent-dark;
  }

  .btn-light {
    @apply bg-neutral text-gray-700 hover:bg-gray-300;
  }

  .btn-outline {
    @apply border border-primary text-primary hover:bg-primary hover:text-white;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-accent text-sm mt-1;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .badge {
    @apply px-2 py-1 text-xs rounded-full;
  }

  .badge-primary {
    @apply bg-primary-lighter text-primary-dark;
  }

  .badge-secondary {
    @apply bg-secondary-light text-secondary-dark;
  }

  .badge-accent {
    @apply bg-accent-light text-accent-dark;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
}

/* Custom animations for auth popups */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}
